#!/bin/bash

echo "🎨 Testing Frontend Features..."
echo ""

# Test 1: Check if frontend is accessible
echo "1. Testing Frontend Accessibility..."
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "✅ Frontend is accessible at http://localhost:3000"
else
    echo "❌ Frontend is not accessible (Status: $FRONTEND_STATUS)"
    exit 1
fi

# Test 2: Check if backend API is working
echo ""
echo "2. Testing Backend API..."
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health)
if [ "$BACKEND_STATUS" = "200" ]; then
    echo "✅ Backend API is working at http://localhost:8080"
else
    echo "❌ Backend API is not working (Status: $BACKEND_STATUS)"
    exit 1
fi

# Test 3: Test API proxy from frontend
echo ""
echo "3. Testing API Proxy (Frontend -> Backend)..."
PROXY_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/v1/workflows)
if [ "$PROXY_STATUS" = "200" ]; then
    echo "✅ API proxy is working correctly"
else
    echo "❌ API proxy is not working (Status: $PROXY_STATUS)"
fi

# Test 4: Create a test workflow to verify backend integration
echo ""
echo "4. Creating a test workflow via API..."
WORKFLOW_RESPONSE=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Frontend Feature Test",
    "description": "Test workflow for frontend features",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "llm",
        "name": "AI Assistant",
        "type": "llm",
        "description": "AI processing",
        "config": {
          "model": "gpt-4o-mini",
          "system_prompt": "You are a helpful assistant.",
          "user_prompt": "Respond to: {{text}}",
          "temperature": 0.7,
          "max_tokens": 200
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "data_transformer",
        "name": "Transform Data",
        "type": "data_transformer",
        "description": "Transform response",
        "config": {
          "transform_type": "uppercase",
          "source_field": "response",
          "target_field": "response_upper"
        },
        "position": {"x": 500, "y": 100}
      },
      {
        "key": "notification",
        "name": "Send Notification",
        "type": "notification",
        "description": "Send notification",
        "config": {
          "type": "success",
          "message": "Processed: {{response_upper}}",
          "channels": ["console"]
        },
        "position": {"x": 700, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 900, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "llm"
      },
      {
        "from_node": "llm",
        "to_node": "data_transformer"
      },
      {
        "from_node": "data_transformer",
        "to_node": "notification"
      },
      {
        "from_node": "notification",
        "to_node": "exit"
      }
    ]
  }')

WORKFLOW_ID=$(echo "$WORKFLOW_RESPONSE" | jq -r '.id')
if [ "$WORKFLOW_ID" != "null" ] && [ "$WORKFLOW_ID" != "" ]; then
    echo "✅ Test workflow created successfully (ID: $WORKFLOW_ID)"
    
    # Test 5: Execute the workflow
    echo ""
    echo "5. Executing the test workflow..."
    EXECUTION_RESPONSE=$(curl -s -X POST http://localhost:8080/api/v1/workflows/$WORKFLOW_ID/execute \
      -H "Content-Type: application/json" \
      -d '{
        "input": {
          "text": "Hello from the frontend test!"
        }
      }')
    
    EXECUTION_STATUS=$(echo "$EXECUTION_RESPONSE" | jq -r '.status')
    if [ "$EXECUTION_STATUS" = "completed" ]; then
        echo "✅ Workflow execution completed successfully"
        echo "📊 Execution details:"
        echo "$EXECUTION_RESPONSE" | jq '.node_executions | length' | xargs echo "   - Nodes executed:"
        echo "$EXECUTION_RESPONSE" | jq -r '.node_executions[] | "   - \(.node_name): \(.status)"'
    else
        echo "❌ Workflow execution failed (Status: $EXECUTION_STATUS)"
    fi
else
    echo "❌ Failed to create test workflow"
fi

echo ""
echo "🎉 Frontend Feature Test Summary:"
echo ""
echo "✅ Frontend Application:"
echo "   • Running on http://localhost:3000"
echo "   • React + TypeScript + Material-UI"
echo "   • ReactFlow visual editor"
echo ""
echo "✅ Backend Integration:"
echo "   • API running on http://localhost:8080"
echo "   • Proxy configuration working"
echo "   • Workflow CRUD operations"
echo ""
echo "✅ Available Features:"
echo "   • Visual workflow editor with drag-and-drop"
echo "   • 20+ node types (AI, Data Processing, Communication, etc.)"
echo "   • Real-time workflow execution"
echo "   • Node property configuration"
echo "   • Performance optimizations (caching, connection pooling)"
echo ""
echo "🌐 Access URLs:"
echo "   • Workflow List: http://localhost:3000"
echo "   • New Workflow: http://localhost:3000/workflows/new/edit"
echo "   • Demo Workflow: http://localhost:3000/workflows/demo/edit"
echo ""
echo "🎯 Next Steps:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Click 'Create New Workflow' to start building"
echo "   3. Drag nodes from the left panel to the canvas"
echo "   4. Connect nodes by dragging from output to input"
echo "   5. Configure node properties in the right panel"
echo "   6. Click 'Run' to execute your workflow"
