#!/bin/bash

echo "Testing Workflow Engine API..."

# Test health endpoint
echo "1. Testing health endpoint..."
curl http://localhost:8080/health
echo ""

# Create a workflow
echo "2. Creating workflow..."
WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Enhanced LLM Test",
    "description": "Test enhanced LLM with real API integration",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "llm",
        "name": "AI Assistant",
        "type": "llm",
        "description": "Enhanced LLM processing",
        "config": {
          "model": "gpt-4o-mini",
          "system_prompt": "You are a helpful AI assistant. Provide clear and concise answers.",
          "user_prompt": "Please answer this question: {{text}}",
          "temperature": 0.7,
          "max_tokens": 500
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 500, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "llm"
      },
      {
        "from_node": "llm",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created workflow with ID: $WORKFLOW_ID"

# Execute the workflow
echo "3. Executing workflow..."
curl -s -X POST http://localhost:8080/api/v1/workflows/$WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "text": "What is the capital of France?"
    }
  }' | jq '.'

echo "Test completed!"
