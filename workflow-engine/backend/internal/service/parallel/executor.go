package parallel

import (
	"context"
	"fmt"
	"time"

	"workflow-engine/internal/domain/workflow"
	"workflow-engine/internal/service/pool"
)

// ExecutionMode defines how nodes should be executed
type ExecutionMode string

const (
	ModeSequential ExecutionMode = "sequential"
	ModeParallel   ExecutionMode = "parallel"
	ModeOptimized  ExecutionMode = "optimized"
)

// Config holds parallel execution configuration
type Config struct {
	Mode           ExecutionMode `json:"mode"`
	MaxConcurrency int           `json:"max_concurrency"`
	Timeout        time.Duration `json:"timeout"`
	EnableBatching bool          `json:"enable_batching"`
	BatchSize      int           `json:"batch_size"`
}

// DefaultConfig returns a default parallel execution configuration
func DefaultConfig() *Config {
	return &Config{
		Mode:           ModeOptimized,
		MaxConcurrency: 10,
		Timeout:        5 * time.Minute,
		EnableBatching: true,
		BatchSize:      5,
	}
}

// Executor handles parallel execution of workflow nodes
type Executor struct {
	config     *Config
	workerPool *pool.WorkerPool
}

// NewExecutor creates a new parallel executor
func NewExecutor(config *Config) *Executor {
	if config == nil {
		config = DefaultConfig()
	}

	return &Executor{
		config:     config,
		workerPool: pool.NewWorkerPool(config.MaxConcurrency),
	}
}

// NodeExecution represents a node execution task
type NodeExecution struct {
	Node     *workflow.NodeSchema
	Input    map[string]interface{}
	Executor workflow.NodeExecutor
	Context  context.Context
}

// ExecutionResult represents the result of a node execution
type ExecutionResult struct {
	NodeKey  string
	Output   map[string]interface{}
	Error    error
	Duration time.Duration
}

// ExecuteNodes executes multiple nodes based on the configured mode
func (e *Executor) ExecuteNodes(ctx context.Context, executions []NodeExecution) (map[string]ExecutionResult, error) {
	switch e.config.Mode {
	case ModeSequential:
		return e.executeSequential(ctx, executions)
	case ModeParallel:
		return e.executeParallel(ctx, executions)
	case ModeOptimized:
		return e.executeOptimized(ctx, executions)
	default:
		return e.executeSequential(ctx, executions)
	}
}

// executeSequential executes nodes one by one
func (e *Executor) executeSequential(ctx context.Context, executions []NodeExecution) (map[string]ExecutionResult, error) {
	results := make(map[string]ExecutionResult)

	for _, exec := range executions {
		start := time.Now()

		output, err := exec.Executor.Execute(exec.Context, exec.Node, exec.Input)

		results[exec.Node.Key] = ExecutionResult{
			NodeKey:  exec.Node.Key,
			Output:   output,
			Error:    err,
			Duration: time.Since(start),
		}

		// Stop on first error in sequential mode
		if err != nil {
			return results, fmt.Errorf("node %s failed: %w", exec.Node.Key, err)
		}
	}

	return results, nil
}

// executeParallel executes all nodes in parallel
func (e *Executor) executeParallel(ctx context.Context, executions []NodeExecution) (map[string]ExecutionResult, error) {
	if len(executions) == 0 {
		return make(map[string]ExecutionResult), nil
	}

	// Create jobs for worker pool
	jobs := make(map[string]func() (interface{}, error))

	for _, exec := range executions {
		// Capture variables for closure
		execution := exec
		jobs[execution.Node.Key] = func() (interface{}, error) {
			start := time.Now()

			output, err := execution.Executor.Execute(execution.Context, execution.Node, execution.Input)

			return ExecutionResult{
				NodeKey:  execution.Node.Key,
				Output:   output,
				Error:    err,
				Duration: time.Since(start),
			}, nil
		}
	}

	// Execute jobs in parallel
	jobResults := e.workerPool.SubmitBatch(jobs)

	// Convert results
	results := make(map[string]ExecutionResult)
	var firstError error

	for nodeKey, jobResult := range jobResults {
		if jobResult.Error != nil {
			if firstError == nil {
				firstError = fmt.Errorf("job %s failed: %w", nodeKey, jobResult.Error)
			}
			results[nodeKey] = ExecutionResult{
				NodeKey: nodeKey,
				Error:   jobResult.Error,
			}
		} else {
			if execResult, ok := jobResult.Result.(ExecutionResult); ok {
				results[nodeKey] = execResult
			} else {
				results[nodeKey] = ExecutionResult{
					NodeKey: nodeKey,
					Error:   fmt.Errorf("invalid result type"),
				}
			}
		}
	}

	return results, firstError
}

// executeOptimized executes nodes with intelligent optimization
func (e *Executor) executeOptimized(ctx context.Context, executions []NodeExecution) (map[string]ExecutionResult, error) {
	if len(executions) == 0 {
		return make(map[string]ExecutionResult), nil
	}

	// Analyze dependencies and group nodes
	groups := e.analyzeAndGroup(executions)

	results := make(map[string]ExecutionResult)

	// Execute groups in order (sequential between groups, parallel within groups)
	for _, group := range groups {
		groupResults, err := e.executeParallel(ctx, group)

		// Merge results
		for key, result := range groupResults {
			results[key] = result
		}

		// Stop on error
		if err != nil {
			return results, err
		}
	}

	return results, nil
}

// analyzeAndGroup analyzes node dependencies and groups them for optimal execution
func (e *Executor) analyzeAndGroup(executions []NodeExecution) [][]NodeExecution {
	// Simple grouping strategy: group by node type for now
	// In a real implementation, this would analyze actual dependencies

	groups := make(map[workflow.NodeType][]NodeExecution)

	for _, exec := range executions {
		nodeType := exec.Node.Type
		groups[nodeType] = append(groups[nodeType], exec)
	}

	// Convert to slice of groups
	var result [][]NodeExecution
	for _, group := range groups {
		// Split large groups into batches if batching is enabled
		if e.config.EnableBatching && len(group) > e.config.BatchSize {
			for i := 0; i < len(group); i += e.config.BatchSize {
				end := i + e.config.BatchSize
				if end > len(group) {
					end = len(group)
				}
				result = append(result, group[i:end])
			}
		} else {
			result = append(result, group)
		}
	}

	return result
}

// ExecuteBatch executes a batch of similar operations
func (e *Executor) ExecuteBatch(ctx context.Context, batchType string, operations []func() (interface{}, error)) ([]interface{}, error) {
	if len(operations) == 0 {
		return nil, nil
	}

	// Create jobs
	jobs := make(map[string]func() (interface{}, error))
	for i, op := range operations {
		jobID := fmt.Sprintf("%s_%d", batchType, i)
		jobs[jobID] = op
	}

	// Execute in parallel
	jobResults := e.workerPool.SubmitBatch(jobs)

	// Collect results in order
	results := make([]interface{}, len(operations))
	var firstError error

	for i := 0; i < len(operations); i++ {
		jobID := fmt.Sprintf("%s_%d", batchType, i)
		jobResult := jobResults[jobID]

		if jobResult.Error != nil && firstError == nil {
			firstError = jobResult.Error
		}

		results[i] = jobResult.Result
	}

	return results, firstError
}

// GetStats returns executor statistics
func (e *Executor) GetStats() map[string]interface{} {
	workerStats := e.workerPool.GetStats()

	return map[string]interface{}{
		"mode":            string(e.config.Mode),
		"max_concurrency": e.config.MaxConcurrency,
		"timeout":         e.config.Timeout.String(),
		"enable_batching": e.config.EnableBatching,
		"batch_size":      e.config.BatchSize,
		"worker_stats":    workerStats,
	}
}

// Shutdown gracefully shuts down the executor
func (e *Executor) Shutdown() {
	if e.workerPool != nil {
		e.workerPool.Shutdown()
	}
}

// OptimizationHints provides hints for execution optimization
type OptimizationHints struct {
	CanParallelize    bool          `json:"can_parallelize"`
	EstimatedDuration time.Duration `json:"estimated_duration"`
	ResourceUsage     ResourceUsage `json:"resource_usage"`
	Dependencies      []string      `json:"dependencies"`
}

// ResourceUsage represents resource usage information
type ResourceUsage struct {
	CPU     float64 `json:"cpu"`
	Memory  int64   `json:"memory"`
	IO      bool    `json:"io"`
	Network bool    `json:"network"`
}

// AnalyzeNode analyzes a node and provides optimization hints
func (e *Executor) AnalyzeNode(node *workflow.NodeSchema) OptimizationHints {
	hints := OptimizationHints{
		CanParallelize:    true,
		EstimatedDuration: 1 * time.Second,
		ResourceUsage: ResourceUsage{
			CPU:     0.1,
			Memory:  1024 * 1024, // 1MB
			IO:      false,
			Network: false,
		},
		Dependencies: []string{},
	}

	// Analyze based on node type
	switch node.Type {
	case workflow.NodeTypeLLM:
		hints.EstimatedDuration = 5 * time.Second
		hints.ResourceUsage.Network = true
		hints.ResourceUsage.CPU = 0.2

	case workflow.NodeTypeDatabaseQuery, workflow.NodeTypeDatabaseCreate,
		workflow.NodeTypeDatabaseUpdate, workflow.NodeTypeDatabaseDelete:
		hints.EstimatedDuration = 2 * time.Second
		hints.ResourceUsage.IO = true
		hints.ResourceUsage.Network = true

	case workflow.NodeTypeKnowledgeRetriever, workflow.NodeTypeKnowledgeWriter:
		hints.EstimatedDuration = 3 * time.Second
		hints.ResourceUsage.Network = true
		hints.ResourceUsage.Memory = 5 * 1024 * 1024 // 5MB

	case workflow.NodeTypeHTTPRequester:
		hints.EstimatedDuration = 2 * time.Second
		hints.ResourceUsage.Network = true

	case workflow.NodeTypeCodeRunner:
		hints.EstimatedDuration = 10 * time.Second
		hints.ResourceUsage.CPU = 0.8
		hints.ResourceUsage.Memory = 10 * 1024 * 1024 // 10MB
		hints.CanParallelize = false                  // Code execution might have side effects

	default:
		hints.EstimatedDuration = 500 * time.Millisecond
	}

	return hints
}
