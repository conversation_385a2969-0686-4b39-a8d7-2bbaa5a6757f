package llm

import (
	"context"
	"fmt"
	"strings"
	"time"

	"workflow-engine/internal/config"
	"workflow-engine/internal/service/fallback"
	"workflow-engine/internal/service/retry"
)

// Provider represents an LLM provider
type Provider string

const (
	ProviderOpenAI    Provider = "openai"
	ProviderAnthropic Provider = "anthropic"
	ProviderGoogle    Provider = "google"
)

// LLMResponse represents a unified LLM response
type LLMResponse struct {
	Content      string                 `json:"content"`
	Model        string                 `json:"model"`
	Provider     Provider               `json:"provider"`
	TokensUsed   int                    `json:"tokens_used"`
	FinishReason string                 `json:"finish_reason"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// LLMRequest represents a unified LLM request
type LLMRequest struct {
	Model        string                 `json:"model"`
	SystemPrompt string                 `json:"system_prompt,omitempty"`
	UserPrompt   string                 `json:"user_prompt"`
	Temperature  float64                `json:"temperature,omitempty"`
	MaxTokens    int                    `json:"max_tokens,omitempty"`
	TopP         float64                `json:"top_p,omitempty"`
	Stream       bool                   `json:"stream,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// Manager manages multiple LLM providers
type Manager struct {
	openai          *OpenAIClient
	anthropic       *AnthropicClient
	config          *config.Config
	retrier         *retry.Retrier
	fallbackHandler *fallback.Handler
}

// NewManager creates a new LLM manager
func NewManager(cfg *config.Config) *Manager {
	// Configure retry strategy for LLM calls
	retryConfig := &retry.Config{
		MaxAttempts:  3,
		InitialDelay: 1 * time.Second,
		MaxDelay:     10 * time.Second,
		Strategy:     retry.StrategyExponential,
		Multiplier:   2.0,
		Jitter:       true,
		RetryableFunc: func(err error) bool {
			if err == nil {
				return false
			}
			errStr := err.Error()
			// Retry on network errors, timeouts, and rate limits
			return contains(errStr, []string{"timeout", "connection", "network", "429", "rate limit", "502", "503", "504"})
		},
	}

	// Configure fallback strategy
	fallbackConfig := &fallback.Config{
		Strategy:    fallback.StrategyMock,
		Timeout:     30 * time.Second,
		EnableCache: true,
		CacheTTL:    5 * time.Minute,
	}

	return &Manager{
		openai:          NewOpenAIClient(cfg.APIs.OpenAI),
		anthropic:       NewAnthropicClient(cfg.APIs.Anthropic),
		config:          cfg,
		retrier:         retry.New(retryConfig),
		fallbackHandler: fallback.New(fallbackConfig),
	}
}

// contains checks if any of the substrings exist in the main string
func contains(str string, substrings []string) bool {
	for _, substr := range substrings {
		if len(str) >= len(substr) {
			for i := 0; i <= len(str)-len(substr); i++ {
				if str[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// GetProvider determines the provider from model name
func (m *Manager) GetProvider(model string) Provider {
	model = strings.ToLower(model)

	if strings.HasPrefix(model, "gpt-") {
		return ProviderOpenAI
	}
	if strings.HasPrefix(model, "claude-") {
		return ProviderAnthropic
	}
	if strings.HasPrefix(model, "gemini-") {
		return ProviderGoogle
	}

	// Default to OpenAI
	return ProviderOpenAI
}

// IsProviderConfigured checks if a provider is properly configured
func (m *Manager) IsProviderConfigured(provider Provider) bool {
	switch provider {
	case ProviderOpenAI:
		return m.openai.IsConfigured()
	case ProviderAnthropic:
		return m.anthropic.IsConfigured()
	case ProviderGoogle:
		// TODO: Implement Google client
		return false
	default:
		return false
	}
}

// GenerateResponse generates a response using the appropriate provider with retry and fallback
func (m *Manager) GenerateResponse(ctx context.Context, req LLMRequest) (*LLMResponse, error) {
	provider := m.GetProvider(req.Model)

	// Set defaults
	if req.Temperature == 0 {
		req.Temperature = 0.7
	}
	if req.MaxTokens == 0 {
		req.MaxTokens = 1000
	}

	// Create cache key for fallback
	cacheKey := fmt.Sprintf("%s:%s:%s", provider, req.Model, req.UserPrompt[:min(50, len(req.UserPrompt))])

	// Execute with fallback support
	result, err := m.fallbackHandler.Execute(ctx, cacheKey,
		func() (interface{}, error) {
			// Primary function with retry
			return m.retrier.DoWithResult(ctx, func() (interface{}, error) {
				response, err := m.generateResponseInternal(ctx, req, provider)
				return response, err
			})
		},
		func() (interface{}, error) {
			// Fallback function - try alternative provider or mock
			return m.generateFallbackResponse(ctx, req, provider)
		},
	)

	if err != nil {
		return nil, err
	}

	response, ok := result.(*LLMResponse)
	if !ok {
		return nil, fmt.Errorf("invalid response type from fallback handler")
	}

	return response, nil
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// generateResponseInternal generates response without retry/fallback logic
func (m *Manager) generateResponseInternal(ctx context.Context, req LLMRequest, provider Provider) (*LLMResponse, error) {
	// Check if provider is configured
	if !m.IsProviderConfigured(provider) {
		return nil, fmt.Errorf("provider %s is not configured", provider)
	}

	switch provider {
	case ProviderOpenAI:
		return m.generateOpenAIResponse(ctx, req)
	case ProviderAnthropic:
		return m.generateAnthropicResponse(ctx, req)
	case ProviderGoogle:
		return nil, fmt.Errorf("Google provider not yet implemented")
	default:
		return nil, fmt.Errorf("unsupported provider: %s", provider)
	}
}

// generateFallbackResponse generates a fallback response
func (m *Manager) generateFallbackResponse(ctx context.Context, req LLMRequest, originalProvider Provider) (*LLMResponse, error) {
	// Try alternative providers first
	providers := []Provider{ProviderOpenAI, ProviderAnthropic}

	for _, provider := range providers {
		if provider != originalProvider && m.IsProviderConfigured(provider) {
			// Try alternative provider
			response, err := m.generateResponseInternal(ctx, req, provider)
			if err == nil {
				// Mark as fallback response
				response.Metadata["fallback"] = true
				response.Metadata["original_provider"] = string(originalProvider)
				return response, nil
			}
		}
	}

	// All providers failed, return mock response
	return &LLMResponse{
		Content:      fmt.Sprintf("Mock response for: %s (Original provider %s unavailable)", req.UserPrompt, originalProvider),
		Model:        req.Model,
		Provider:     originalProvider,
		TokensUsed:   100,
		FinishReason: "fallback",
		Metadata: map[string]interface{}{
			"fallback":          true,
			"fallback_type":     "mock",
			"original_provider": string(originalProvider),
			"timestamp":         time.Now().Format(time.RFC3339),
		},
	}, nil
}

// generateOpenAIResponse generates response using OpenAI
func (m *Manager) generateOpenAIResponse(ctx context.Context, req LLMRequest) (*LLMResponse, error) {
	// Validate model
	if !m.openai.ValidateModel(req.Model) {
		return nil, fmt.Errorf("unsupported OpenAI model: %s", req.Model)
	}

	// Create OpenAI request
	openaiReq := m.openai.CreateSimpleRequest(
		req.Model,
		req.SystemPrompt,
		req.UserPrompt,
		req.Temperature,
		req.MaxTokens,
	)

	// Add optional parameters
	if req.TopP > 0 {
		openaiReq.TopP = &req.TopP
	}

	// Send request
	resp, err := m.openai.ChatCompletion(ctx, openaiReq)
	if err != nil {
		return nil, fmt.Errorf("OpenAI API error: %w", err)
	}

	// Extract response
	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no choices in OpenAI response")
	}

	choice := resp.Choices[0]

	return &LLMResponse{
		Content:      choice.Message.Content,
		Model:        resp.Model,
		Provider:     ProviderOpenAI,
		TokensUsed:   resp.Usage.TotalTokens,
		FinishReason: choice.FinishReason,
		Metadata: map[string]interface{}{
			"id":                resp.ID,
			"created":           resp.Created,
			"prompt_tokens":     resp.Usage.PromptTokens,
			"completion_tokens": resp.Usage.CompletionTokens,
		},
	}, nil
}

// generateAnthropicResponse generates response using Anthropic
func (m *Manager) generateAnthropicResponse(ctx context.Context, req LLMRequest) (*LLMResponse, error) {
	// Validate model
	if !m.anthropic.ValidateModel(req.Model) {
		return nil, fmt.Errorf("unsupported Anthropic model: %s", req.Model)
	}

	// Create Anthropic request
	anthropicReq := m.anthropic.CreateSimpleRequest(
		req.Model,
		req.SystemPrompt,
		req.UserPrompt,
		req.Temperature,
		req.MaxTokens,
	)

	// Add optional parameters
	if req.TopP > 0 {
		anthropicReq.TopP = &req.TopP
	}

	// Send request
	resp, err := m.anthropic.CreateMessage(ctx, anthropicReq)
	if err != nil {
		return nil, fmt.Errorf("Anthropic API error: %w", err)
	}

	// Extract text content
	content := m.anthropic.ExtractTextContent(resp)

	return &LLMResponse{
		Content:      content,
		Model:        resp.Model,
		Provider:     ProviderAnthropic,
		TokensUsed:   resp.Usage.InputTokens + resp.Usage.OutputTokens,
		FinishReason: resp.StopReason,
		Metadata: map[string]interface{}{
			"id":            resp.ID,
			"input_tokens":  resp.Usage.InputTokens,
			"output_tokens": resp.Usage.OutputTokens,
			"stop_sequence": resp.StopSequence,
		},
	}, nil
}

// GetAvailableModels returns all available models across providers
func (m *Manager) GetAvailableModels() map[Provider][]string {
	models := make(map[Provider][]string)

	if m.openai.IsConfigured() {
		models[ProviderOpenAI] = m.openai.GetModels()
	}

	if m.anthropic.IsConfigured() {
		models[ProviderAnthropic] = m.anthropic.GetModels()
	}

	return models
}

// ValidateModel checks if a model is supported by any configured provider
func (m *Manager) ValidateModel(model string) bool {
	provider := m.GetProvider(model)

	switch provider {
	case ProviderOpenAI:
		return m.openai.IsConfigured() && m.openai.ValidateModel(model)
	case ProviderAnthropic:
		return m.anthropic.IsConfigured() && m.anthropic.ValidateModel(model)
	case ProviderGoogle:
		// TODO: Implement Google validation
		return false
	default:
		return false
	}
}

// EstimateTokens estimates token count for a given text
func (m *Manager) EstimateTokens(text string, provider Provider) int {
	switch provider {
	case ProviderOpenAI:
		return m.openai.EstimateTokens(text)
	case ProviderAnthropic:
		return m.anthropic.EstimateTokens(text)
	default:
		// Fallback estimation
		return len(text) / 4
	}
}

// GetProviderStatus returns the configuration status of all providers
func (m *Manager) GetProviderStatus() map[Provider]bool {
	return map[Provider]bool{
		ProviderOpenAI:    m.openai.IsConfigured(),
		ProviderAnthropic: m.anthropic.IsConfigured(),
		ProviderGoogle:    false, // TODO: Implement Google client
	}
}
