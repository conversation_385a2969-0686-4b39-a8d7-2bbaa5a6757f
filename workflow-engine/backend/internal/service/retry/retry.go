package retry

import (
	"context"
	"fmt"
	"math"
	"time"
)

// Strategy defines the retry strategy
type Strategy string

const (
	StrategyFixed       Strategy = "fixed"
	StrategyExponential Strategy = "exponential"
	StrategyLinear      Strategy = "linear"
)

// Config holds retry configuration
type Config struct {
	MaxAttempts   int              `json:"max_attempts"`
	InitialDelay  time.Duration    `json:"initial_delay"`
	MaxDelay      time.Duration    `json:"max_delay"`
	Strategy      Strategy         `json:"strategy"`
	Multiplier    float64          `json:"multiplier"`
	Jitter        bool             `json:"jitter"`
	RetryableFunc func(error) bool `json:"-"`
}

// DefaultConfig returns a default retry configuration
func DefaultConfig() *Config {
	return &Config{
		MaxAttempts:   3,
		InitialDelay:  1 * time.Second,
		MaxDelay:      30 * time.Second,
		Strategy:      StrategyExponential,
		Multiplier:    2.0,
		Jitter:        true,
		RetryableFunc: DefaultRetryableFunc,
	}
}

// DefaultRetryableFunc determines if an error is retryable
func DefaultRetryableFunc(err error) bool {
	if err == nil {
		return false
	}

	// Add specific error type checks here
	errStr := err.Error()

	// Network errors are usually retryable
	if contains(errStr, []string{"timeout", "connection", "network", "temporary"}) {
		return true
	}

	// HTTP 5xx errors are retryable
	if contains(errStr, []string{"500", "502", "503", "504"}) {
		return true
	}

	// Rate limiting is retryable
	if contains(errStr, []string{"rate limit", "429", "quota"}) {
		return true
	}

	return false
}

// contains checks if any of the substrings exist in the main string
func contains(str string, substrings []string) bool {
	for _, substr := range substrings {
		if len(str) >= len(substr) {
			for i := 0; i <= len(str)-len(substr); i++ {
				if str[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// Retrier handles retry logic
type Retrier struct {
	config *Config
}

// New creates a new retrier with the given configuration
func New(config *Config) *Retrier {
	if config == nil {
		config = DefaultConfig()
	}
	return &Retrier{config: config}
}

// Do executes the function with retry logic
func (r *Retrier) Do(ctx context.Context, fn func() error) error {
	var lastErr error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// Execute the function
		err := fn()
		if err == nil {
			return nil // Success
		}

		lastErr = err

		// Check if error is retryable
		if !r.config.RetryableFunc(err) {
			return fmt.Errorf("non-retryable error: %w", err)
		}

		// Don't wait after the last attempt
		if attempt == r.config.MaxAttempts {
			break
		}

		// Calculate delay
		delay := r.calculateDelay(attempt)

		// Wait with context cancellation support
		select {
		case <-ctx.Done():
			return fmt.Errorf("retry cancelled: %w", ctx.Err())
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", r.config.MaxAttempts, lastErr)
}

// DoWithResult executes the function with retry logic and returns a result
func (r *Retrier) DoWithResult(ctx context.Context, fn func() (interface{}, error)) (interface{}, error) {
	var lastErr error
	var result interface{}

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// Execute the function
		res, err := fn()
		if err == nil {
			return res, nil // Success
		}

		lastErr = err

		// Check if error is retryable
		if !r.config.RetryableFunc(err) {
			return result, fmt.Errorf("non-retryable error: %w", err)
		}

		// Don't wait after the last attempt
		if attempt == r.config.MaxAttempts {
			break
		}

		// Calculate delay
		delay := r.calculateDelay(attempt)

		// Wait with context cancellation support
		select {
		case <-ctx.Done():
			return result, fmt.Errorf("retry cancelled: %w", ctx.Err())
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return result, fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", r.config.MaxAttempts, lastErr)
}

// calculateDelay calculates the delay for the given attempt
func (r *Retrier) calculateDelay(attempt int) time.Duration {
	var delay time.Duration

	switch r.config.Strategy {
	case StrategyFixed:
		delay = r.config.InitialDelay

	case StrategyLinear:
		delay = time.Duration(float64(r.config.InitialDelay) * float64(attempt))

	case StrategyExponential:
		delay = time.Duration(float64(r.config.InitialDelay) * math.Pow(r.config.Multiplier, float64(attempt-1)))

	default:
		delay = r.config.InitialDelay
	}

	// Apply maximum delay limit
	if delay > r.config.MaxDelay {
		delay = r.config.MaxDelay
	}

	// Apply jitter if enabled
	if r.config.Jitter {
		jitterAmount := float64(delay) * 0.1 // 10% jitter
		// Simple jitter based on attempt number (deterministic but varied)
		jitterFactor := float64(attempt%3-1) * 0.1 // -0.1, 0, or 0.1
		jitter := time.Duration(jitterAmount * jitterFactor)
		delay += jitter

		// Ensure delay is not negative
		if delay < 0 {
			delay = r.config.InitialDelay
		}
	}

	return delay
}

// CircuitBreakerState represents the state of a circuit breaker
type CircuitBreakerState int

const (
	StateClosed CircuitBreakerState = iota
	StateOpen
	StateHalfOpen
)

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	maxFailures     int
	resetTimeout    time.Duration
	state           CircuitBreakerState
	failures        int
	lastFailureTime time.Time
	onStateChange   func(from, to CircuitBreakerState)
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        StateClosed,
	}
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(fn func() error) error {
	if cb.state == StateOpen {
		if time.Since(cb.lastFailureTime) > cb.resetTimeout {
			cb.setState(StateHalfOpen)
		} else {
			return fmt.Errorf("circuit breaker is open")
		}
	}

	err := fn()

	if err != nil {
		cb.onFailure()
		return err
	}

	cb.onSuccess()
	return nil
}

// onSuccess handles successful execution
func (cb *CircuitBreaker) onSuccess() {
	cb.failures = 0
	if cb.state == StateHalfOpen {
		cb.setState(StateClosed)
	}
}

// onFailure handles failed execution
func (cb *CircuitBreaker) onFailure() {
	cb.failures++
	cb.lastFailureTime = time.Now()

	if cb.failures >= cb.maxFailures {
		cb.setState(StateOpen)
	}
}

// setState changes the circuit breaker state
func (cb *CircuitBreaker) setState(state CircuitBreakerState) {
	if cb.onStateChange != nil {
		cb.onStateChange(cb.state, state)
	}
	cb.state = state
}

// GetState returns the current state
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	return cb.state
}
