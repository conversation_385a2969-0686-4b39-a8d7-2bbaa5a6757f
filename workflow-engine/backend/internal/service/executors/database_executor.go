package executors

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"workflow-engine/internal/domain/workflow"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
)

// DatabaseExecutor handles database-related node execution
type DatabaseExecutor struct {
	connections map[string]*sql.DB
}

// NewDatabaseExecutor creates a new database executor
func NewDatabaseExecutor() *DatabaseExecutor {
	return &DatabaseExecutor{
		connections: make(map[string]*sql.DB),
	}
}

// ExecuteDatabaseQuery executes a database query node
func (e *DatabaseExecutor) ExecuteDatabaseQuery(ctx context.Context, node *workflow.NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Type assert config to map[string]interface{}
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config format: expected map[string]interface{}")
	}

	// Get connection string
	connectionStr, ok := config["connection"].(string)
	if !ok {
		return nil, fmt.Errorf("connection string is required")
	}

	// Get query
	query, ok := config["query"].(string)
	if !ok {
		return nil, fmt.Errorf("query is required")
	}

	// Get database connection
	db, err := e.getConnection(connectionStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Prepare parameters
	var args []interface{}
	if params, ok := input["parameters"].(map[string]interface{}); ok {
		// Replace named parameters in query
		for key, value := range params {
			placeholder := fmt.Sprintf("{{%s}}", key)
			if strings.Contains(query, placeholder) {
				query = strings.ReplaceAll(query, placeholder, "?")
				args = append(args, value)
			}
		}
	}

	// Execute query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	// Get column names
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	// Scan results
	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	return map[string]interface{}{
		"results": results,
		"count":   len(results),
	}, nil
}

// ExecuteDatabaseCreate executes a database create node
func (e *DatabaseExecutor) ExecuteDatabaseCreate(ctx context.Context, node *workflow.NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Type assert config to map[string]interface{}
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config format: expected map[string]interface{}")
	}

	// Get connection string
	connectionStr, ok := config["connection"].(string)
	if !ok {
		return nil, fmt.Errorf("connection string is required")
	}

	// Get table name
	tableName, ok := config["table"].(string)
	if !ok {
		return nil, fmt.Errorf("table name is required")
	}

	// Get data to insert
	data, ok := input["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("data is required")
	}

	// Get database connection
	db, err := e.getConnection(connectionStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Build insert query
	var columns []string
	var placeholders []string
	var values []interface{}

	for key, value := range data {
		columns = append(columns, key)
		placeholders = append(placeholders, "?")
		values = append(values, value)
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		tableName,
		strings.Join(columns, ", "),
		strings.Join(placeholders, ", "))

	// Execute insert
	result, err := db.ExecContext(ctx, query, values...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute insert: %w", err)
	}

	// Get inserted ID and affected rows
	insertedID, _ := result.LastInsertId()
	affectedRows, _ := result.RowsAffected()

	return map[string]interface{}{
		"inserted_id":   insertedID,
		"affected_rows": affectedRows,
	}, nil
}

// ExecuteDatabaseUpdate executes a database update node
func (e *DatabaseExecutor) ExecuteDatabaseUpdate(ctx context.Context, node *workflow.NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Type assert config to map[string]interface{}
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config format: expected map[string]interface{}")
	}

	// Get connection string
	connectionStr, ok := config["connection"].(string)
	if !ok {
		return nil, fmt.Errorf("connection string is required")
	}

	// Get table name
	tableName, ok := config["table"].(string)
	if !ok {
		return nil, fmt.Errorf("table name is required")
	}

	// Get WHERE clause
	whereClause, ok := config["where_clause"].(string)
	if !ok {
		return nil, fmt.Errorf("where clause is required")
	}

	// Get data to update
	data, ok := input["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("data is required")
	}

	// Get database connection
	db, err := e.getConnection(connectionStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Build update query
	var setParts []string
	var values []interface{}

	for key, value := range data {
		setParts = append(setParts, fmt.Sprintf("%s = ?", key))
		values = append(values, value)
	}

	query := fmt.Sprintf("UPDATE %s SET %s WHERE %s",
		tableName,
		strings.Join(setParts, ", "),
		whereClause)

	// Execute update
	result, err := db.ExecContext(ctx, query, values...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute update: %w", err)
	}

	// Get affected rows
	affectedRows, _ := result.RowsAffected()

	return map[string]interface{}{
		"affected_rows": affectedRows,
		"success":       affectedRows > 0,
	}, nil
}

// ExecuteDatabaseDelete executes a database delete node
func (e *DatabaseExecutor) ExecuteDatabaseDelete(ctx context.Context, node *workflow.NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Type assert config to map[string]interface{}
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config format: expected map[string]interface{}")
	}

	// Get connection string
	connectionStr, ok := config["connection"].(string)
	if !ok {
		return nil, fmt.Errorf("connection string is required")
	}

	// Get table name
	tableName, ok := config["table"].(string)
	if !ok {
		return nil, fmt.Errorf("table name is required")
	}

	// Get WHERE clause
	whereClause, ok := config["where_clause"].(string)
	if !ok {
		return nil, fmt.Errorf("where clause is required")
	}

	// Check confirmation if required
	if confirmDelete, ok := config["confirm_delete"].(bool); ok && confirmDelete {
		if confirmed, ok := input["confirmed"].(bool); !ok || !confirmed {
			return nil, fmt.Errorf("delete operation requires confirmation")
		}
	}

	// Get database connection
	db, err := e.getConnection(connectionStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Build delete query
	query := fmt.Sprintf("DELETE FROM %s WHERE %s", tableName, whereClause)

	// Execute delete
	result, err := db.ExecContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute delete: %w", err)
	}

	// Get affected rows
	deletedRows, _ := result.RowsAffected()

	return map[string]interface{}{
		"deleted_rows": deletedRows,
		"success":      deletedRows > 0,
	}, nil
}

// getConnection gets or creates a database connection
func (e *DatabaseExecutor) getConnection(connectionStr string) (*sql.DB, error) {
	if db, exists := e.connections[connectionStr]; exists {
		return db, nil
	}

	// Parse connection string to determine driver
	var driverName string
	if strings.HasPrefix(connectionStr, "mysql://") {
		driverName = "mysql"
		connectionStr = strings.TrimPrefix(connectionStr, "mysql://")
	} else if strings.HasPrefix(connectionStr, "postgres://") || strings.HasPrefix(connectionStr, "postgresql://") {
		driverName = "postgres"
	} else if strings.HasPrefix(connectionStr, "sqlite://") {
		driverName = "sqlite3"
		connectionStr = strings.TrimPrefix(connectionStr, "sqlite://")
	} else {
		// Default to SQLite for simple file paths
		driverName = "sqlite3"
	}

	// Open connection
	db, err := sql.Open(driverName, connectionStr)
	if err != nil {
		return nil, err
	}

	// Test connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, err
	}

	// Cache connection
	e.connections[connectionStr] = db

	return db, nil
}

// Close closes all database connections
func (e *DatabaseExecutor) Close() error {
	for _, db := range e.connections {
		if err := db.Close(); err != nil {
			return err
		}
	}
	e.connections = make(map[string]*sql.DB)
	return nil
}
