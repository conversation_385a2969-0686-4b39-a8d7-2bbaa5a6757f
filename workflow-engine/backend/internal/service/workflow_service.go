package service

import (
	"context"
	"fmt"
	"time"

	"workflow-engine/internal/domain/workflow"
	"workflow-engine/internal/service/llm"
	"workflow-engine/internal/service/pool"

	"github.com/google/uuid"
)

// WorkflowService provides workflow management and execution services
type WorkflowService struct {
	engine         *workflow.Engine
	repository     WorkflowRepository
	llmManager     *llm.Manager
	httpClientPool *pool.HTTPClientPool
	cachePool      *pool.CachePool
}

// WorkflowRepository defines the interface for workflow persistence
type WorkflowRepository interface {
	CreateWorkflow(ctx context.Context, schema *workflow.WorkflowSchema) error
	GetWorkflow(ctx context.Context, id string) (*workflow.WorkflowSchema, error)
	UpdateWorkflow(ctx context.Context, schema *workflow.WorkflowSchema) error
	DeleteWorkflow(ctx context.Context, id string) error
	ListWorkflows(ctx context.Context, page, limit int, status string) ([]*workflow.WorkflowSchema, int64, error)

	CreateExecution(ctx context.Context, execution *workflow.WorkflowExecution) error
	GetExecution(ctx context.Context, id string) (*workflow.WorkflowExecution, error)
	UpdateExecution(ctx context.Context, execution *workflow.WorkflowExecution) error
	ListExecutions(ctx context.Context, workflowID string, page, limit int) ([]*workflow.WorkflowExecution, int64, error)
}

// NewWorkflowService creates a new workflow service
func NewWorkflowService(repository WorkflowRepository) *WorkflowService {
	return NewWorkflowServiceWithLLM(repository, nil)
}

// NewWorkflowServiceWithLLM creates a new workflow service with LLM manager
func NewWorkflowServiceWithLLM(repository WorkflowRepository, llmManager *llm.Manager) *WorkflowService {
	engine := workflow.NewEngine()

	// Initialize performance optimization components
	httpClientPool := pool.NewHTTPClientPool(pool.DefaultConfig())
	cacheConfig := &pool.CacheConfig{
		DefaultTTL:      5 * time.Minute,
		CleanupInterval: 1 * time.Minute,
		MaxSize:         1000,
	}
	cachePool := pool.NewCachePool(cacheConfig)

	// Register default node executors
	engine.RegisterNodeExecutor(&workflow.EntryNodeExecutor{})
	engine.RegisterNodeExecutor(&workflow.ExitNodeExecutor{})
	engine.RegisterNodeExecutor(&workflow.SelectorNodeExecutor{})
	engine.RegisterNodeExecutor(&workflow.VariableAssignerExecutor{})
	engine.RegisterNodeExecutor(&workflow.TextProcessorExecutor{})
	engine.RegisterNodeExecutor(&workflow.ContinueNodeExecutor{})
	engine.RegisterNodeExecutor(&workflow.BreakNodeExecutor{})

	// Register enhanced node executors with LLM manager
	engine.RegisterNodeExecutor(workflow.NewLLMNodeExecutor(llmManager))
	engine.RegisterNodeExecutor(&workflow.DatabaseQueryExecutor{})
	engine.RegisterNodeExecutor(&workflow.DatabaseCreateExecutor{})
	engine.RegisterNodeExecutor(&workflow.DatabaseUpdateExecutor{})
	engine.RegisterNodeExecutor(&workflow.DatabaseDeleteExecutor{})
	engine.RegisterNodeExecutor(&workflow.KnowledgeRetrieverExecutor{})
	engine.RegisterNodeExecutor(&workflow.KnowledgeWriterExecutor{})
	engine.RegisterNodeExecutor(&workflow.InputReceiverExecutor{})
	engine.RegisterNodeExecutor(&workflow.OutputEmitterExecutor{})
	engine.RegisterNodeExecutor(&workflow.IntentDetectorExecutor{})

	// Register new utility node executors
	engine.RegisterNodeExecutor(&workflow.EmailSenderExecutor{})
	engine.RegisterNodeExecutor(&workflow.FileProcessorExecutor{})
	engine.RegisterNodeExecutor(&workflow.ImageProcessorExecutor{})
	engine.RegisterNodeExecutor(&workflow.WebScraperExecutor{})
	engine.RegisterNodeExecutor(&workflow.SchedulerExecutor{})
	engine.RegisterNodeExecutor(&workflow.NotificationExecutor{})
	engine.RegisterNodeExecutor(&workflow.DataTransformerExecutor{})
	engine.RegisterNodeExecutor(&workflow.ConditionalRouterExecutor{})
	engine.RegisterNodeExecutor(&workflow.ErrorHandlerExecutor{})

	return &WorkflowService{
		engine:         engine,
		repository:     repository,
		llmManager:     llmManager,
		httpClientPool: httpClientPool,
		cachePool:      cachePool,
	}
}

// Start starts the workflow service
func (s *WorkflowService) Start(ctx context.Context) error {
	return s.engine.Start(ctx)
}

// Stop stops the workflow service
func (s *WorkflowService) Stop() error {
	return s.engine.Stop()
}

// CreateWorkflow creates a new workflow
func (s *WorkflowService) CreateWorkflow(ctx context.Context, schema *workflow.WorkflowSchema) (string, error) {
	// Generate ID and set timestamps
	schema.ID = uuid.New().String()
	now := time.Now()
	schema.CreatedAt = &now
	schema.UpdatedAt = &now

	// Validate schema
	if err := schema.Validate(); err != nil {
		return "", fmt.Errorf("invalid workflow schema: %w", err)
	}

	// Save to repository
	if err := s.repository.CreateWorkflow(ctx, schema); err != nil {
		return "", fmt.Errorf("failed to create workflow: %w", err)
	}

	return schema.ID, nil
}

// GetWorkflow retrieves a workflow by ID
func (s *WorkflowService) GetWorkflow(ctx context.Context, id string) (*workflow.WorkflowSchema, error) {
	schema, err := s.repository.GetWorkflow(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get workflow: %w", err)
	}

	// Initialize the schema
	schema.Init()

	return schema, nil
}

// UpdateWorkflow updates an existing workflow
func (s *WorkflowService) UpdateWorkflow(ctx context.Context, schema *workflow.WorkflowSchema) error {
	// Validate schema
	if err := schema.Validate(); err != nil {
		return fmt.Errorf("invalid workflow schema: %w", err)
	}

	// Update timestamp
	now := time.Now()
	schema.UpdatedAt = &now

	// Save to repository
	if err := s.repository.UpdateWorkflow(ctx, schema); err != nil {
		return fmt.Errorf("failed to update workflow: %w", err)
	}

	return nil
}

// DeleteWorkflow deletes a workflow by ID
func (s *WorkflowService) DeleteWorkflow(ctx context.Context, id string) error {
	if err := s.repository.DeleteWorkflow(ctx, id); err != nil {
		return fmt.Errorf("failed to delete workflow: %w", err)
	}

	return nil
}

// ListWorkflows lists workflows with pagination
func (s *WorkflowService) ListWorkflows(ctx context.Context, page, limit int, status string) ([]*workflow.WorkflowSchema, int64, error) {
	workflows, total, err := s.repository.ListWorkflows(ctx, page, limit, status)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list workflows: %w", err)
	}

	// Initialize all schemas
	for _, schema := range workflows {
		schema.Init()
	}

	return workflows, total, nil
}

// ExecuteWorkflow executes a workflow with the given input
func (s *WorkflowService) ExecuteWorkflow(ctx context.Context, workflowID string, input map[string]interface{}, options *workflow.ExecutionOptions) (*workflow.WorkflowExecution, error) {
	// Get workflow schema
	schema, err := s.GetWorkflow(ctx, workflowID)
	if err != nil {
		return nil, fmt.Errorf("failed to get workflow: %w", err)
	}

	// Execute workflow
	execution, err := s.engine.ExecuteWorkflow(ctx, schema, input, options)
	if err != nil {
		return nil, fmt.Errorf("failed to execute workflow: %w", err)
	}

	// Save execution to repository
	if err := s.repository.CreateExecution(ctx, execution); err != nil {
		return nil, fmt.Errorf("failed to save execution: %w", err)
	}

	return execution, nil
}

// GetExecution retrieves an execution by ID
func (s *WorkflowService) GetExecution(ctx context.Context, id string) (*workflow.WorkflowExecution, error) {
	// Try to get from engine first (for running executions)
	if execution, err := s.engine.GetExecution(id); err == nil {
		return execution, nil
	}

	// Get from repository
	execution, err := s.repository.GetExecution(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get execution: %w", err)
	}

	return execution, nil
}

// CancelExecution cancels a running execution
func (s *WorkflowService) CancelExecution(ctx context.Context, id string) error {
	if err := s.engine.CancelExecution(id); err != nil {
		return fmt.Errorf("failed to cancel execution: %w", err)
	}

	return nil
}

// GetExecutionEvents returns a channel of execution events
func (s *WorkflowService) GetExecutionEvents(ctx context.Context, executionID string) (<-chan *workflow.ExecutionEvent, error) {
	// Create a channel for events
	eventChan := make(chan *workflow.ExecutionEvent, 100)

	// Register an event handler that filters events for this execution
	handler := &executionEventHandler{
		executionID: executionID,
		eventChan:   eventChan,
	}

	s.engine.RegisterEventHandler(handler)

	// Close the channel when context is done
	go func() {
		<-ctx.Done()
		close(eventChan)
	}()

	return eventChan, nil
}

// executionEventHandler filters events for a specific execution
type executionEventHandler struct {
	executionID string
	eventChan   chan *workflow.ExecutionEvent
}

func (h *executionEventHandler) HandleEvent(ctx context.Context, event *workflow.ExecutionEvent) error {
	if event.ExecutionID == h.executionID {
		select {
		case h.eventChan <- event:
		default:
			// Channel is full, drop the event
		}
	}
	return nil
}

// PublishWorkflow publishes a workflow (changes status to published)
func (s *WorkflowService) PublishWorkflow(ctx context.Context, id string) error {
	schema, err := s.GetWorkflow(ctx, id)
	if err != nil {
		return err
	}

	schema.Status = workflow.WorkflowStatusPublished

	return s.UpdateWorkflow(ctx, schema)
}

// ArchiveWorkflow archives a workflow (changes status to archived)
func (s *WorkflowService) ArchiveWorkflow(ctx context.Context, id string) error {
	schema, err := s.GetWorkflow(ctx, id)
	if err != nil {
		return err
	}

	schema.Status = workflow.WorkflowStatusArchived

	return s.UpdateWorkflow(ctx, schema)
}

// CloneWorkflow creates a copy of an existing workflow
func (s *WorkflowService) CloneWorkflow(ctx context.Context, id string, newName string) (string, error) {
	// Get original workflow
	original, err := s.GetWorkflow(ctx, id)
	if err != nil {
		return "", err
	}

	// Clone the schema
	clone, err := original.Clone()
	if err != nil {
		return "", fmt.Errorf("failed to clone workflow: %w", err)
	}

	// Update clone properties
	clone.ID = ""
	clone.Name = newName
	clone.Status = workflow.WorkflowStatusDraft
	clone.CreatedAt = nil
	clone.UpdatedAt = nil

	// Create the cloned workflow
	return s.CreateWorkflow(ctx, clone)
}

// GetPerformanceStats returns performance statistics
func (s *WorkflowService) GetPerformanceStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if s.httpClientPool != nil {
		stats["http_client_pool"] = s.httpClientPool.GetStats()
	}

	if s.cachePool != nil {
		// Get stats from a sample cache
		sampleCache := s.cachePool.GetCache("sample")
		stats["cache_pool"] = sampleCache.GetStats()
	}

	stats["optimization_enabled"] = true
	stats["timestamp"] = time.Now().Format(time.RFC3339)

	return stats
}

// ClearCache clears all caches
func (s *WorkflowService) ClearCache() {
	if s.httpClientPool != nil {
		s.httpClientPool.CleanupIdleConnections()
	}
	// Note: Individual caches are cleaned up automatically based on TTL
}

// GetCachedResult retrieves a cached result
func (s *WorkflowService) GetCachedResult(key string) (interface{}, bool) {
	if s.cachePool == nil {
		return nil, false
	}

	cache := s.cachePool.GetCache("workflow_results")
	return cache.Get(key)
}

// SetCachedResult stores a result in cache
func (s *WorkflowService) SetCachedResult(key string, result interface{}, ttl time.Duration) {
	if s.cachePool == nil {
		return
	}

	cache := s.cachePool.GetCache("workflow_results")
	cache.Set(key, result, ttl)
}
