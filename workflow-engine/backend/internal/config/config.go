package config

import (
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Redis    RedisConfig
	APIs     APIConfig
	Security SecurityConfig
	Logging  LoggingConfig
	Storage  StorageConfig
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port string
	Host string
	Env  string
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Type     string
	Host     string
	Port     string
	Name     string
	User     string
	Password string
	SSLMode  string
	// Connection strings for different databases
	MySQLDSN    string
	PostgresDSN string
	SQLitePath  string
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

// APIConfig holds external API configurations
type APIConfig struct {
	OpenAI    OpenAIConfig
	Anthropic AnthropicConfig
	Google    GoogleConfig
	Vector    VectorConfig
	Email     EmailConfig
}

// OpenAIConfig holds OpenAI API configuration
type OpenAIConfig struct {
	APIKey  string
	BaseURL string
	Timeout time.Duration
}

// AnthropicConfig holds Anthropic API configuration
type AnthropicConfig struct {
	APIKey  string
	BaseURL string
	Timeout time.Duration
}

// GoogleConfig holds Google API configuration
type GoogleConfig struct {
	APIKey  string
	BaseURL string
	Timeout time.Duration
}

// VectorConfig holds vector database configuration
type VectorConfig struct {
	PineconeAPIKey     string
	PineconeEnv        string
	WeaviateURL        string
	WeaviateAPIKey     string
}

// EmailConfig holds email service configuration
type EmailConfig struct {
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	SMTPFrom     string
}

// SecurityConfig holds security configuration
type SecurityConfig struct {
	JWTSecret   string
	CORSOrigins string
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string
	Format string
}

// StorageConfig holds storage configuration
type StorageConfig struct {
	Type string
	Path string
}

// Load loads configuration from environment variables
func Load() *Config {
	return &Config{
		Server: ServerConfig{
			Port: getEnv("PORT", "8080"),
			Host: getEnv("HOST", "0.0.0.0"),
			Env:  getEnv("ENV", "development"),
		},
		Database: DatabaseConfig{
			Type:        getEnv("DB_TYPE", "sqlite"),
			Host:        getEnv("DB_HOST", "localhost"),
			Port:        getEnv("DB_PORT", "5432"),
			Name:        getEnv("DB_NAME", "workflow_engine"),
			User:        getEnv("DB_USER", "workflow"),
			Password:    getEnv("DB_PASSWORD", ""),
			SSLMode:     getEnv("DB_SSL_MODE", "disable"),
			MySQLDSN:    getEnv("MYSQL_DSN", ""),
			PostgresDSN: getEnv("POSTGRES_DSN", ""),
			SQLitePath:  getEnv("SQLITE_PATH", "./data/workflow.db"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvInt("REDIS_DB", 0),
		},
		APIs: APIConfig{
			OpenAI: OpenAIConfig{
				APIKey:  getEnv("OPENAI_API_KEY", ""),
				BaseURL: getEnv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
				Timeout: getEnvDuration("OPENAI_TIMEOUT", 60*time.Second),
			},
			Anthropic: AnthropicConfig{
				APIKey:  getEnv("ANTHROPIC_API_KEY", ""),
				BaseURL: getEnv("ANTHROPIC_BASE_URL", "https://api.anthropic.com"),
				Timeout: getEnvDuration("ANTHROPIC_TIMEOUT", 60*time.Second),
			},
			Google: GoogleConfig{
				APIKey:  getEnv("GOOGLE_API_KEY", ""),
				BaseURL: getEnv("GOOGLE_BASE_URL", "https://generativelanguage.googleapis.com"),
				Timeout: getEnvDuration("GOOGLE_TIMEOUT", 60*time.Second),
			},
			Vector: VectorConfig{
				PineconeAPIKey: getEnv("PINECONE_API_KEY", ""),
				PineconeEnv:    getEnv("PINECONE_ENVIRONMENT", ""),
				WeaviateURL:    getEnv("WEAVIATE_URL", ""),
				WeaviateAPIKey: getEnv("WEAVIATE_API_KEY", ""),
			},
			Email: EmailConfig{
				SMTPHost:     getEnv("SMTP_HOST", ""),
				SMTPPort:     getEnvInt("SMTP_PORT", 587),
				SMTPUsername: getEnv("SMTP_USERNAME", ""),
				SMTPPassword: getEnv("SMTP_PASSWORD", ""),
				SMTPFrom:     getEnv("SMTP_FROM", ""),
			},
		},
		Security: SecurityConfig{
			JWTSecret:   getEnv("JWT_SECRET", "default-secret"),
			CORSOrigins: getEnv("CORS_ORIGINS", "http://localhost:3000"),
		},
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
		Storage: StorageConfig{
			Type: getEnv("STORAGE_TYPE", "local"),
			Path: getEnv("STORAGE_PATH", "./data"),
		},
	}
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// IsDevelopment returns true if running in development mode
func (c *Config) IsDevelopment() bool {
	return c.Server.Env == "development"
}

// IsProduction returns true if running in production mode
func (c *Config) IsProduction() bool {
	return c.Server.Env == "production"
}

// GetServerAddress returns the full server address
func (c *Config) GetServerAddress() string {
	return c.Server.Host + ":" + c.Server.Port
}
