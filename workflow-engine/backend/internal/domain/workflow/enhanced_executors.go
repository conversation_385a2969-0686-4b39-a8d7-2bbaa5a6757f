package workflow

import (
	"context"
	"fmt"
	"strings"

	"workflow-engine/internal/service/llm"
)

// LLMNodeExecutor executes LLM nodes with enhanced capabilities
type LLMNodeExecutor struct {
	llmManager *llm.Manager
}

// NewLLMNodeExecutor creates a new LLM node executor
func NewLLMNodeExecutor(llmManager *llm.Manager) *LLMNodeExecutor {
	return &LLMNodeExecutor{
		llmManager: llmManager,
	}
}

func (e *LLMNodeExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid LLM configuration")
	}

	// Get model
	model := "gpt-4o-mini"
	if m, ok := config["model"].(string); ok {
		model = m
	}

	// Get system prompt
	systemPrompt := ""
	if sp, ok := config["system_prompt"].(string); ok {
		systemPrompt = sp
	}

	// Build user prompt template
	userPromptTemplate := "{{user_input}}"
	if prompt, ok := config["user_prompt"].(string); ok {
		userPromptTemplate = prompt
	}

	// Render user prompt with input variables
	userPrompt, err := e.renderTemplate(userPromptTemplate, input)
	if err != nil {
		return nil, fmt.Errorf("failed to render user prompt: %w", err)
	}

	// Get optional parameters
	temperature := 0.7
	if temp, ok := config["temperature"].(float64); ok {
		temperature = temp
	}

	maxTokens := 1000
	if tokens, ok := config["max_tokens"].(float64); ok {
		maxTokens = int(tokens)
	}

	// Use real LLM API if manager is available, otherwise fallback to mock
	if e.llmManager != nil {
		// Create LLM request
		llmReq := llm.LLMRequest{
			Model:        model,
			SystemPrompt: systemPrompt,
			UserPrompt:   userPrompt,
			Temperature:  temperature,
			MaxTokens:    maxTokens,
		}

		// Generate response
		resp, err := e.llmManager.GenerateResponse(ctx, llmReq)
		if err != nil {
			// Fallback to mock response on error
			return e.mockResponse(model, userPrompt, err), nil
		}

		return map[string]interface{}{
			"response":      resp.Content,
			"tokens_used":   resp.TokensUsed,
			"finish_reason": resp.FinishReason,
			"model_used":    resp.Model,
			"provider":      string(resp.Provider),
		}, nil
	}

	// Fallback to mock response
	return e.mockResponse(model, userPrompt, nil), nil
}

// renderTemplate renders a template string with input variables
func (e *LLMNodeExecutor) renderTemplate(templateStr string, data map[string]interface{}) (string, error) {
	// Simple string replacement for now
	result := templateStr
	for key, value := range data {
		placeholder := fmt.Sprintf("{{%s}}", key)
		if valueStr, ok := value.(string); ok {
			result = strings.ReplaceAll(result, placeholder, valueStr)
		} else {
			result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
		}
	}
	return result, nil
}

// mockResponse creates a mock response for fallback
func (e *LLMNodeExecutor) mockResponse(model, userPrompt string, err error) map[string]interface{} {
	response := fmt.Sprintf("Mock LLM Response using %s: %s", model, userPrompt)
	if err != nil {
		response = fmt.Sprintf("Mock LLM Response (API Error: %v) using %s: %s", err, model, userPrompt)
	}

	return map[string]interface{}{
		"response":      response,
		"tokens_used":   100,
		"finish_reason": "stop",
		"model_used":    model,
		"provider":      "mock",
	}
}

func (e *LLMNodeExecutor) GetType() NodeType {
	return NodeTypeLLM
}

// DatabaseQueryExecutor executes database query nodes
type DatabaseQueryExecutor struct{}

func (e *DatabaseQueryExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid database configuration")
	}

	// Get connection and query
	_, ok = config["connection"].(string)
	if !ok {
		return nil, fmt.Errorf("connection is required")
	}

	_, ok = config["query"].(string)
	if !ok {
		return nil, fmt.Errorf("query is required")
	}

	// Mock database query for now
	results := []map[string]interface{}{
		{"id": 1, "name": "Sample Record", "value": "test"},
	}

	return map[string]interface{}{
		"results": results,
		"count":   len(results),
	}, nil
}

func (e *DatabaseQueryExecutor) GetType() NodeType {
	return NodeTypeDatabaseQuery
}

// DatabaseCreateExecutor executes database create nodes
type DatabaseCreateExecutor struct{}

func (e *DatabaseCreateExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid database configuration")
	}

	// Validate required config
	if _, ok := config["connection"].(string); !ok {
		return nil, fmt.Errorf("connection is required")
	}
	if _, ok := config["table"].(string); !ok {
		return nil, fmt.Errorf("table is required")
	}

	// Validate input data
	if _, ok := input["data"].(map[string]interface{}); !ok {
		return nil, fmt.Errorf("data is required")
	}

	// Mock database insert
	return map[string]interface{}{
		"inserted_id":   123,
		"affected_rows": 1,
	}, nil
}

func (e *DatabaseCreateExecutor) GetType() NodeType {
	return NodeTypeDatabaseCreate
}

// DatabaseUpdateExecutor executes database update nodes
type DatabaseUpdateExecutor struct{}

func (e *DatabaseUpdateExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid database configuration")
	}

	// Validate required config
	if _, ok := config["connection"].(string); !ok {
		return nil, fmt.Errorf("connection is required")
	}
	if _, ok := config["table"].(string); !ok {
		return nil, fmt.Errorf("table is required")
	}
	if _, ok := config["where_clause"].(string); !ok {
		return nil, fmt.Errorf("where_clause is required")
	}

	// Validate input data
	if _, ok := input["data"].(map[string]interface{}); !ok {
		return nil, fmt.Errorf("data is required")
	}

	// Mock database update
	return map[string]interface{}{
		"affected_rows": 1,
		"success":       true,
	}, nil
}

func (e *DatabaseUpdateExecutor) GetType() NodeType {
	return NodeTypeDatabaseUpdate
}

// DatabaseDeleteExecutor executes database delete nodes
type DatabaseDeleteExecutor struct{}

func (e *DatabaseDeleteExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid database configuration")
	}

	// Validate required config
	if _, ok := config["connection"].(string); !ok {
		return nil, fmt.Errorf("connection is required")
	}
	if _, ok := config["table"].(string); !ok {
		return nil, fmt.Errorf("table is required")
	}
	if _, ok := config["where_clause"].(string); !ok {
		return nil, fmt.Errorf("where_clause is required")
	}

	// Check confirmation if required
	if confirmDelete, ok := config["confirm_delete"].(bool); ok && confirmDelete {
		if confirmed, ok := input["confirmed"].(bool); !ok || !confirmed {
			return nil, fmt.Errorf("delete operation requires confirmation")
		}
	}

	// Mock database delete
	return map[string]interface{}{
		"deleted_rows": 1,
		"success":      true,
	}, nil
}

func (e *DatabaseDeleteExecutor) GetType() NodeType {
	return NodeTypeDatabaseDelete
}

// KnowledgeRetrieverExecutor executes knowledge retriever nodes
type KnowledgeRetrieverExecutor struct{}

func (e *KnowledgeRetrieverExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid knowledge configuration")
	}

	// Validate required config
	if _, ok := config["knowledge_base"].(string); !ok {
		return nil, fmt.Errorf("knowledge_base is required")
	}

	// Validate input - accept either "query" or "text"
	var query string
	if q, ok := input["query"].(string); ok {
		query = q
	} else if t, ok := input["text"].(string); ok {
		query = t
	} else {
		return nil, fmt.Errorf("query or text is required")
	}

	// Mock knowledge retrieval
	results := []map[string]interface{}{
		{
			"id":      "doc1",
			"content": "Sample knowledge base content related to: " + query,
			"score":   0.95,
			"metadata": map[string]interface{}{
				"source": "knowledge_base",
				"type":   "document",
			},
		},
	}

	return map[string]interface{}{
		"results":          results,
		"relevance_scores": []float64{0.95},
		"total_count":      len(results),
	}, nil
}

func (e *KnowledgeRetrieverExecutor) GetType() NodeType {
	return NodeTypeKnowledgeRetriever
}

// KnowledgeWriterExecutor executes knowledge writer nodes
type KnowledgeWriterExecutor struct{}

func (e *KnowledgeWriterExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid knowledge configuration")
	}

	// Validate required config
	if _, ok := config["knowledge_base"].(string); !ok {
		return nil, fmt.Errorf("knowledge_base is required")
	}

	// Validate input
	content, ok := input["content"].(string)
	if !ok {
		return nil, fmt.Errorf("content is required")
	}

	// Mock knowledge writing
	documentID := fmt.Sprintf("doc_%d", len(content))

	return map[string]interface{}{
		"document_id": documentID,
		"success":     true,
	}, nil
}

func (e *KnowledgeWriterExecutor) GetType() NodeType {
	return NodeTypeKnowledgeWriter
}

// InputReceiverExecutor executes input receiver nodes
type InputReceiverExecutor struct{}

func (e *InputReceiverExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Mock user input reception
	userInput := "Sample user input"
	if existing, ok := input["user_input"]; ok {
		userInput = fmt.Sprintf("%v", existing)
	}

	return map[string]interface{}{
		"user_input": userInput,
		"timestamp":  "2023-12-01T10:00:00Z",
	}, nil
}

func (e *InputReceiverExecutor) GetType() NodeType {
	return NodeTypeInputReceiver
}

// OutputEmitterExecutor executes output emitter nodes
type OutputEmitterExecutor struct{}

func (e *OutputEmitterExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Get content to emit
	content := input["content"]
	if content == nil {
		content = input
	}

	// Mock output emission
	return map[string]interface{}{
		"success":    true,
		"message_id": "msg_123",
	}, nil
}

func (e *OutputEmitterExecutor) GetType() NodeType {
	return NodeTypeOutputEmitter
}

// IntentDetectorExecutor executes intent detector nodes
type IntentDetectorExecutor struct{}

func (e *IntentDetectorExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Get text to analyze
	text, ok := input["text"].(string)
	if !ok {
		return nil, fmt.Errorf("text is required")
	}

	// Mock intent detection
	intent := "general_inquiry"
	confidence := 0.85

	// Simple keyword-based intent detection
	if len(text) > 0 {
		// Check for help keywords
		if text == "help" || text == "support" {
			intent = "help_request"
			confidence = 0.92
		}
	}

	return map[string]interface{}{
		"intent":     intent,
		"confidence": confidence,
		"entities":   []map[string]interface{}{},
	}, nil
}

func (e *IntentDetectorExecutor) GetType() NodeType {
	return NodeTypeIntentDetector
}
