package workflow

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
)

// Engine represents the workflow execution engine
type Engine struct {
	mu         sync.RWMutex
	executions map[string]*WorkflowExecution
	eventChan  chan *ExecutionEvent
	stopChan   chan struct{}

	// Node executors
	nodeExecutors map[NodeType]NodeExecutor

	// Event handlers
	eventHandlers []EventHandler

	// Performance optimization components (interfaces to avoid circular imports)
	optimizationEnabled bool
}

// NodeExecutor defines the interface for executing different node types
type NodeExecutor interface {
	Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error)
	GetType() NodeType
}

// EventHandler defines the interface for handling execution events
type EventHandler interface {
	HandleEvent(ctx context.Context, event *ExecutionEvent) error
}

// NewEngine creates a new workflow execution engine
func NewEngine() *Engine {
	return &Engine{
		executions:    make(map[string]*WorkflowExecution),
		eventChan:     make(chan *ExecutionEvent, 1000),
		stop<PERSON>han:      make(chan struct{}),
		nodeExecutors: make(map[NodeType]NodeExecutor),
		eventHandlers: make([]EventHandler, 0),
	}
}

// RegisterNodeExecutor registers a node executor for a specific node type
func (e *Engine) RegisterNodeExecutor(executor NodeExecutor) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.nodeExecutors[executor.GetType()] = executor
}

// RegisterEventHandler registers an event handler
func (e *Engine) RegisterEventHandler(handler EventHandler) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.eventHandlers = append(e.eventHandlers, handler)
}

// Start starts the execution engine
func (e *Engine) Start(ctx context.Context) error {
	go e.eventLoop(ctx)
	return nil
}

// Stop stops the execution engine
func (e *Engine) Stop() error {
	close(e.stopChan)
	return nil
}

// ExecuteWorkflow executes a workflow with the given input
func (e *Engine) ExecuteWorkflow(ctx context.Context, schema *WorkflowSchema, input map[string]interface{}, options *ExecutionOptions) (*WorkflowExecution, error) {
	// Validate workflow schema
	if err := schema.Validate(); err != nil {
		return nil, fmt.Errorf("invalid workflow schema: %w", err)
	}

	// Create execution instance
	execution := &WorkflowExecution{
		ID:         uuid.New().String(),
		WorkflowID: schema.ID,
		Version:    schema.Version,
		Status:     ExecutionStatusRunning,
		Input:      input,
		StartedAt:  time.Now(),
		Context:    make(map[string]interface{}),
	}

	// Store execution
	e.mu.Lock()
	e.executions[execution.ID] = execution
	e.mu.Unlock()

	// Emit workflow started event
	e.emitEvent(&ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execution.ID,
		Type:        EventTypeWorkflowStarted,
		Timestamp:   time.Now(),
		Data: map[string]interface{}{
			"workflow_id": schema.ID,
			"input":       input,
		},
	})

	// Execute workflow
	if options != nil && options.Async {
		go e.executeWorkflowAsync(ctx, schema, execution, options)
		return execution, nil
	}

	return e.executeWorkflowSync(ctx, schema, execution, options)
}

// GetExecution returns an execution by ID
func (e *Engine) GetExecution(id string) (*WorkflowExecution, error) {
	e.mu.RLock()
	defer e.mu.RUnlock()

	execution, exists := e.executions[id]
	if !exists {
		return nil, fmt.Errorf("execution not found: %s", id)
	}

	return execution, nil
}

// CancelExecution cancels a running execution
func (e *Engine) CancelExecution(id string) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	execution, exists := e.executions[id]
	if !exists {
		return fmt.Errorf("execution not found: %s", id)
	}

	if execution.IsCompleted() {
		return fmt.Errorf("execution already completed: %s", id)
	}

	execution.Complete(ExecutionStatusCancelled, nil, "execution cancelled")

	// Emit workflow cancelled event
	e.emitEvent(&ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execution.ID,
		Type:        EventTypeWorkflowCancelled,
		Timestamp:   time.Now(),
	})

	return nil
}

// executeWorkflowSync executes a workflow synchronously
func (e *Engine) executeWorkflowSync(ctx context.Context, schema *WorkflowSchema, execution *WorkflowExecution, options *ExecutionOptions) (*WorkflowExecution, error) {
	defer func() {
		if !execution.IsCompleted() {
			execution.Complete(ExecutionStatusFailed, nil, "execution interrupted")
		}
	}()

	// Create execution context
	execCtx := &ExecutionContext{
		ExecutionID: execution.ID,
		WorkflowID:  schema.ID,
		Variables:   make(map[string]interface{}),
		NodeStates:  make(map[NodeKey]*NodeState),
		Options:     options,
	}

	// Copy input to variables
	if inputMap, ok := execution.Input.(map[string]interface{}); ok {
		for k, v := range inputMap {
			execCtx.Variables[k] = v
		}
	}

	// Execute workflow
	output, err := e.executeNodes(ctx, schema, execCtx)
	if err != nil {
		execution.Complete(ExecutionStatusFailed, nil, err.Error())
		e.emitEvent(&ExecutionEvent{
			ID:          uuid.New().String(),
			ExecutionID: execution.ID,
			Type:        EventTypeWorkflowFailed,
			Timestamp:   time.Now(),
			Data: map[string]interface{}{
				"error": err.Error(),
			},
		})
		return execution, err
	}

	execution.Complete(ExecutionStatusSuccess, output, "")
	e.emitEvent(&ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execution.ID,
		Type:        EventTypeWorkflowCompleted,
		Timestamp:   time.Now(),
		Data: map[string]interface{}{
			"output": output,
		},
	})

	return execution, nil
}

// executeWorkflowAsync executes a workflow asynchronously
func (e *Engine) executeWorkflowAsync(ctx context.Context, schema *WorkflowSchema, execution *WorkflowExecution, options *ExecutionOptions) {
	_, _ = e.executeWorkflowSync(ctx, schema, execution, options)
}

// executeNodes executes the workflow nodes in the correct order
func (e *Engine) executeNodes(ctx context.Context, schema *WorkflowSchema, execCtx *ExecutionContext) (map[string]interface{}, error) {
	// Find entry node
	var entryNode *NodeSchema
	for _, node := range schema.Nodes {
		if node.Type == NodeTypeEntry {
			entryNode = node
			break
		}
	}

	if entryNode == nil {
		return nil, fmt.Errorf("no entry node found")
	}

	// Execute nodes starting from entry
	return e.executeNode(ctx, schema, entryNode, execCtx)
}

// executeNode executes a single node and its successors
func (e *Engine) executeNode(ctx context.Context, schema *WorkflowSchema, node *NodeSchema, execCtx *ExecutionContext) (map[string]interface{}, error) {
	// Check if node already executed
	if state, exists := execCtx.NodeStates[node.Key]; exists && state.Status == NodeExecutionStatusSuccess {
		return state.Output.(map[string]interface{}), nil
	}

	// Get node executor
	executor, exists := e.nodeExecutors[node.Type]
	if !exists {
		return nil, fmt.Errorf("no executor found for node type: %s", node.Type)
	}

	// Prepare input
	input, err := e.prepareNodeInput(node, execCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare input for node %s: %w", node.Key, err)
	}

	// Create node execution
	nodeExec := &NodeExecution{
		ID:        uuid.New().String(),
		NodeKey:   node.Key,
		NodeName:  node.Name,
		NodeType:  node.Type,
		Status:    NodeExecutionStatusRunning,
		Input:     input,
		StartedAt: time.Now(),
	}

	// Add to execution
	execution, _ := e.GetExecution(execCtx.ExecutionID)
	execution.AddNodeExecution(nodeExec)

	// Update node state
	execCtx.NodeStates[node.Key] = &NodeState{
		NodeKey:   node.Key,
		Status:    NodeExecutionStatusRunning,
		Input:     input,
		StartedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Emit node started event
	e.emitEvent(&ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execCtx.ExecutionID,
		Type:        EventTypeNodeStarted,
		Timestamp:   time.Now(),
		Data: map[string]interface{}{
			"node_key":  node.Key,
			"node_type": node.Type,
			"input":     input,
		},
	})

	// Execute node
	output, err := executor.Execute(ctx, node, input)
	if err != nil {
		nodeExec.Complete(NodeExecutionStatusFailed, nil, err.Error())
		execCtx.NodeStates[node.Key].Status = NodeExecutionStatusFailed
		execCtx.NodeStates[node.Key].Error = err.Error()
		execCtx.NodeStates[node.Key].UpdatedAt = time.Now()

		e.emitEvent(&ExecutionEvent{
			ID:          uuid.New().String(),
			ExecutionID: execCtx.ExecutionID,
			Type:        EventTypeNodeFailed,
			Timestamp:   time.Now(),
			Data: map[string]interface{}{
				"node_key": node.Key,
				"error":    err.Error(),
			},
		})

		return nil, err
	}

	// Complete node execution
	nodeExec.Complete(NodeExecutionStatusSuccess, output, "")
	execCtx.NodeStates[node.Key].Status = NodeExecutionStatusSuccess
	execCtx.NodeStates[node.Key].Output = output
	execCtx.NodeStates[node.Key].UpdatedAt = time.Now()

	// Emit node completed event
	e.emitEvent(&ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execCtx.ExecutionID,
		Type:        EventTypeNodeCompleted,
		Timestamp:   time.Now(),
		Data: map[string]interface{}{
			"node_key": node.Key,
			"output":   output,
		},
	})

	// If this is an exit node, return the output
	if node.Type == NodeTypeExit {
		return output, nil
	}

	// Find and execute next nodes
	nextNodes := e.findNextNodes(schema, node.Key)
	for _, nextNode := range nextNodes {
		_, err := e.executeNode(ctx, schema, nextNode, execCtx)
		if err != nil {
			return nil, err
		}
	}

	return output, nil
}

// prepareNodeInput prepares the input for a node based on its input sources
func (e *Engine) prepareNodeInput(node *NodeSchema, execCtx *ExecutionContext) (map[string]interface{}, error) {
	input := make(map[string]interface{})

	// If no input sources defined, use simple data flow from previous nodes
	if len(node.InputSources) == 0 {
		// For entry nodes, use the initial input
		if node.Type == NodeTypeEntry {
			for k, v := range execCtx.Variables {
				input[k] = v
			}
		} else {
			// For other nodes, merge outputs from all previous nodes
			for _, state := range execCtx.NodeStates {
				if state.Status == NodeExecutionStatusSuccess {
					if outputMap, ok := state.Output.(map[string]interface{}); ok {
						for k, v := range outputMap {
							input[k] = v
						}
					}
				}
			}
			// Also include initial variables
			for k, v := range execCtx.Variables {
				input[k] = v
			}
		}
		return input, nil
	}

	// Process explicit input sources
	for _, source := range node.InputSources {
		if len(source.Path) == 0 {
			continue
		}

		key := source.Path[0]

		if source.Source == nil {
			continue
		}

		switch source.Source.Type {
		case "constant":
			input[key] = source.Source.Data
		case "reference":
			if source.Source.Ref != nil {
				if state, exists := execCtx.NodeStates[source.Source.Ref.FromNodeKey]; exists {
					if outputMap, ok := state.Output.(map[string]interface{}); ok {
						if value, exists := outputMap[source.Source.Ref.FromPath]; exists {
							input[key] = value
						}
					}
				}
			}
		case "variable":
			if value, exists := execCtx.Variables[key]; exists {
				input[key] = value
			}
		}
	}

	return input, nil
}

// findNextNodes finds the next nodes to execute after the given node
func (e *Engine) findNextNodes(schema *WorkflowSchema, nodeKey NodeKey) []*NodeSchema {
	var nextNodes []*NodeSchema

	for _, conn := range schema.Connections {
		if conn.FromNode == nodeKey {
			if nextNode := schema.GetNode(conn.ToNode); nextNode != nil {
				nextNodes = append(nextNodes, nextNode)
			}
		}
	}

	return nextNodes
}

// emitEvent emits an execution event
func (e *Engine) emitEvent(event *ExecutionEvent) {
	select {
	case e.eventChan <- event:
	default:
		// Channel is full, drop the event
	}
}

// eventLoop processes execution events
func (e *Engine) eventLoop(ctx context.Context) {
	for {
		select {
		case event := <-e.eventChan:
			for _, handler := range e.eventHandlers {
				if err := handler.HandleEvent(ctx, event); err != nil {
					// Log error but continue processing
					fmt.Printf("Error handling event: %v\n", err)
				}
			}
		case <-e.stopChan:
			return
		case <-ctx.Done():
			return
		}
	}
}
