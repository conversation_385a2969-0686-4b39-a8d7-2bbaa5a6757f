package main

import (
	"bufio"
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"workflow-engine/internal/api/routes"
	"workflow-engine/internal/config"
	"workflow-engine/internal/service"
	"workflow-engine/internal/service/llm"
	"workflow-engine/pkg/repository"
)

func main() {
	// Load environment variables from .env file if it exists
	loadEnvFile()

	// Load configuration
	cfg := config.Load()

	// Initialize LLM manager
	llmManager := llm.NewManager(cfg)

	// Initialize repository (using in-memory implementation for now)
	repo := repository.NewInMemoryRepository()

	// Initialize workflow service with LLM manager
	workflowService := service.NewWorkflowServiceWithLLM(repo, llmManager)

	// Start workflow service
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := workflowService.Start(ctx); err != nil {
		log.Fatalf("Failed to start workflow service: %v", err)
	}

	// Setup routes
	router := routes.SetupRoutes(workflowService)

	// Configure server
	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Println("Starting server on :8080")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// Stop workflow service
	if err := workflowService.Stop(); err != nil {
		log.Printf("Error stopping workflow service: %v", err)
	}

	log.Println("Server exited")
}

// loadEnvFile loads environment variables from .env file
func loadEnvFile() {
	file, err := os.Open(".env")
	if err != nil {
		// .env file doesn't exist, that's okay
		return
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			if os.Getenv(key) == "" {
				os.Setenv(key, value)
			}
		}
	}
}
