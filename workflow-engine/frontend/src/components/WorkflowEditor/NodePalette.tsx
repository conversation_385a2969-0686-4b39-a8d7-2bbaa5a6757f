import React from 'react';
import {
  Box,
  Typography,
} from '@mui/material';
import { getNodeCategories, getNodesByCategory } from '../nodes/nodeConfigs';

const NodePalette: React.FC = () => {
  const categories = getNodeCategories();

  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <Box sx={{
      height: '100%',
      overflow: 'auto',
      backgroundColor: '#fafbfc',
      borderRight: '1px solid #e5e7eb',
      display: 'flex',
      flexDirection: 'column',
    }}>
      {/* Header */}
      <Box sx={{
        p: 2,
        borderBottom: '1px solid #e5e7eb',
        backgroundColor: '#ffffff',
      }}>
        <Typography
          variant="h6"
          sx={{
            fontSize: '16px',
            fontWeight: 600,
            color: '#111827',
            mb: 0,
          }}
        >
          Components
        </Typography>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, p: 2 }}>
      
      {categories.map((category) => {
        const nodes = getNodesByCategory(category);
        
        return (
          <Box key={category} sx={{ mb: 3 }}>
            {/* Category Header */}
            <Typography
              variant="subtitle2"
              sx={{
                fontSize: '12px',
                fontWeight: 600,
                color: '#6b7280',
                textTransform: 'uppercase',
                letterSpacing: '0.8px',
                mb: 1.5,
                px: 0.5,
              }}
            >
              {category}
            </Typography>

            {/* Category Items */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {nodes.map(({ type, config }) => {
                // Coze Studio 风格的颜色映射
                const getCozeColor = (nodeType: string) => {
                  const colorMap: { [key: string]: string } = {
                    'entry': '#22c55e',
                    'exit': '#ef4444',
                    'llm': '#3b82f6',
                    'data_transformer': '#8b5cf6',
                    'notification': '#f59e0b',
                    'email_sender': '#06b6d4',
                    'conditional_router': '#ec4899',
                    'error_handler': '#f97316',
                    'scheduler': '#84cc16',
                    'file_processor': '#6b7280',
                    'image_processor': '#d946ef',
                    'web_scraper': '#14b8a6',
                    'database_query': '#0ea5e9',
                    'database_create': '#10b981',
                    'database_update': '#f59e0b',
                    'database_delete': '#ef4444',
                    'knowledge_retriever': '#8b5cf6',
                    'knowledge_writer': '#06b6d4',
                    'input_receiver': '#22c55e',
                    'output_emitter': '#ef4444',
                    'intent_detector': '#3b82f6',
                    'variable_assigner': '#6b7280',
                    'text_processor': '#84cc16',
                    'http_requester': '#f97316',
                    'code_runner': '#8b5cf6',
                    'sub_workflow': '#6b7280',
                  };
                  return colorMap[nodeType] || config.color || '#6b7280';
                };

                const nodeColor = getCozeColor(type);

                return (
                  <Box
                    key={type}
                    draggable
                    onDragStart={(e) => onDragStart(e, type)}
                    sx={{
                      p: 1.5,
                      cursor: 'grab',
                      backgroundColor: '#ffffff',
                      border: '1px solid #e5e7eb',
                      borderRadius: '6px',
                      transition: 'all 0.15s ease',
                      '&:hover': {
                        backgroundColor: '#f9fafb',
                        borderColor: nodeColor,
                        boxShadow: `0 1px 3px rgba(0, 0, 0, 0.1)`,
                      },
                      '&:active': {
                        cursor: 'grabbing',
                        transform: 'scale(0.98)',
                      },
                    }}
                  >
                    <Box display="flex" alignItems="center" gap={1.5}>
                      <Box
                        sx={{
                          width: 24,
                          height: 24,
                          borderRadius: '4px',
                          backgroundColor: `${nodeColor}20`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '12px',
                          flexShrink: 0,
                        }}
                      >
                        {config.icon}
                      </Box>
                      <Box flex={1} minWidth={0}>
                        <Typography
                          variant="body2"
                          fontWeight={500}
                          sx={{
                            fontSize: '13px',
                            color: '#111827',
                            lineHeight: 1.2,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {config.label}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            fontSize: '11px',
                            color: '#6b7280',
                            lineHeight: 1.2,
                            display: 'block',
                            mt: 0.25,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {config.description}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
              </Box>
          </Box>
        );
      })}

      {/* Help Text */}
      <Box sx={{
        p: 2,
        mt: 2,
        mx: 2,
        backgroundColor: '#f0f9ff',
        borderRadius: '6px',
        border: '1px solid #bae6fd',
      }}>
        <Typography
          variant="caption"
          sx={{
            fontSize: '11px',
            color: '#0369a1',
            lineHeight: 1.4,
          }}
        >
          💡 Drag and drop components onto the canvas to build your workflow
        </Typography>
      </Box>
      </Box>
    </Box>
  );
};

export default NodePalette;
