import React from 'react';
import {
  Box,
  Button,
  IconButton,
  <PERSON>pography,
  Divider,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Save as SaveIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  FitScreen as FitScreenIcon,
  Settings as SettingsIcon,
  Share as ShareIcon,
} from '@mui/icons-material';

interface ToolbarProps {
  workflowName: string;
  isExecuting: boolean;
  canUndo: boolean;
  canRedo: boolean;
  onExecute: () => void;
  onStop: () => void;
  onSave: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onFitView: () => void;
  onSettings: () => void;
  onShare: () => void;
}

const Toolbar: React.FC<ToolbarProps> = ({
  workflowName,
  isExecuting,
  canUndo,
  canRedo,
  onExecute,
  onStop,
  onSave,
  onUndo,
  onRedo,
  onZoomIn,
  onZoomOut,
  onFitView,
  onSettings,
  onShare,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 4,
        py: 2,
        backgroundColor: '#ffffff',
        borderBottom: '1px solid #e5e7eb',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
        zIndex: 1000,
        minHeight: 64,
      }}
    >
      {/* Left Section - Workflow Info */}
      <Box display="flex" alignItems="center" gap={2}>
        <Typography
          variant="h6"
          sx={{
            fontSize: '18px',
            fontWeight: 600,
            color: '#1a1a1a',
          }}
        >
          {workflowName}
        </Typography>
        <Chip
          label="Draft"
          size="small"
          sx={{
            backgroundColor: '#fef3c7',
            color: '#92400e',
            fontSize: '11px',
            height: 22,
            fontWeight: 500,
            '& .MuiChip-label': {
              px: 1.5,
            },
          }}
        />
      </Box>

      {/* Center Section - Main Actions */}
      <Box display="flex" alignItems="center" gap={1}>
        {/* Execution Controls */}
        {isExecuting ? (
          <Button
            variant="contained"
            startIcon={<StopIcon />}
            onClick={onStop}
            sx={{
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              '&:hover': {
                backgroundColor: '#dc2626',
              },
            }}
          >
            Stop
          </Button>
        ) : (
          <Button
            variant="contained"
            startIcon={<PlayIcon />}
            onClick={onExecute}
            sx={{
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              py: 1,
              fontSize: '14px',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              '&:hover': {
                backgroundColor: '#2563eb',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
              },
            }}
          >
            Run
          </Button>
        )}

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* Edit Actions */}
        <Tooltip title="Undo">
          <span>
            <IconButton
              onClick={onUndo}
              disabled={!canUndo}
              sx={{
                color: canUndo ? '#6b7280' : '#d1d5db',
                '&:hover': {
                  backgroundColor: '#f3f4f6',
                },
              }}
            >
              <UndoIcon fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>

        <Tooltip title="Redo">
          <span>
            <IconButton
              onClick={onRedo}
              disabled={!canRedo}
              sx={{
                color: canRedo ? '#6b7280' : '#d1d5db',
                '&:hover': {
                  backgroundColor: '#f3f4f6',
                },
              }}
            >
              <RedoIcon fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* View Controls */}
        <Tooltip title="Zoom In">
          <IconButton
            onClick={onZoomIn}
            sx={{
              color: '#6b7280',
              '&:hover': {
                backgroundColor: '#f3f4f6',
              },
            }}
          >
            <ZoomInIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Zoom Out">
          <IconButton
            onClick={onZoomOut}
            sx={{
              color: '#6b7280',
              '&:hover': {
                backgroundColor: '#f3f4f6',
              },
            }}
          >
            <ZoomOutIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Fit to Screen">
          <IconButton
            onClick={onFitView}
            sx={{
              color: '#6b7280',
              '&:hover': {
                backgroundColor: '#f3f4f6',
              },
            }}
          >
            <FitScreenIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Right Section - Secondary Actions */}
      <Box display="flex" alignItems="center" gap={1}>
        <Button
          variant="outlined"
          startIcon={<SaveIcon />}
          onClick={onSave}
          sx={{
            borderColor: '#d1d5db',
            color: '#6b7280',
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 500,
            '&:hover': {
              borderColor: '#9ca3af',
              backgroundColor: '#f9fafb',
            },
          }}
        >
          Save
        </Button>

        <Tooltip title="Share">
          <IconButton
            onClick={onShare}
            sx={{
              color: '#6b7280',
              '&:hover': {
                backgroundColor: '#f3f4f6',
              },
            }}
          >
            <ShareIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Settings">
          <IconButton
            onClick={onSettings}
            sx={{
              color: '#6b7280',
              '&:hover': {
                backgroundColor: '#f3f4f6',
              },
            }}
          >
            <SettingsIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default Toolbar;
