import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Box, Typography } from '@mui/material';
import { NodeSchema, NodeExecutionStatus } from '../../types/workflow';
import { nodeConfigs } from './nodeConfigs';

interface CustomNodeData extends NodeSchema {
  executionStatus?: NodeExecutionStatus;
  isSelected?: boolean;
}

const CustomNode: React.FC<NodeProps<CustomNodeData>> = ({ data, selected }) => {
  const config = nodeConfigs[data.type];

  const getStatusColor = (status?: NodeExecutionStatus) => {
    switch (status) {
      case NodeExecutionStatus.RUNNING:
        return '#ff9800';
      case NodeExecutionStatus.SUCCESS:
        return '#4caf50';
      case NodeExecutionStatus.FAILED:
        return '#f44336';
      case NodeExecutionStatus.SKIPPED:
        return '#9e9e9e';
      default:
        return 'transparent';
    }
  };

  const hasInputs = Object.keys(config.inputs).length > 0;
  const hasOutputs = Object.keys(config.outputs).length > 0;

  // Coze Studio 风格的颜色映射
  const getCozeColor = (nodeType: string) => {
    const colorMap: { [key: string]: string } = {
      'entry': '#22c55e',
      'exit': '#ef4444',
      'llm': '#3b82f6',
      'data_transformer': '#8b5cf6',
      'notification': '#f59e0b',
      'email_sender': '#06b6d4',
      'conditional_router': '#ec4899',
      'error_handler': '#f97316',
      'scheduler': '#84cc16',
      'file_processor': '#6b7280',
      'image_processor': '#d946ef',
      'web_scraper': '#14b8a6',
      'database_query': '#0ea5e9',
      'database_create': '#10b981',
      'database_update': '#f59e0b',
      'database_delete': '#ef4444',
      'knowledge_retriever': '#8b5cf6',
      'knowledge_writer': '#06b6d4',
      'input_receiver': '#22c55e',
      'output_emitter': '#ef4444',
      'intent_detector': '#3b82f6',
      'variable_assigner': '#6b7280',
      'text_processor': '#84cc16',
      'http_requester': '#f97316',
      'code_runner': '#8b5cf6',
      'sub_workflow': '#6b7280',
    };
    return colorMap[nodeType] || config.color || '#6b7280';
  };

  const nodeColor = getCozeColor(data.type);

  return (
    <Box
      sx={{
        background: '#ffffff',
        border: selected ? `2px solid ${nodeColor}` : '1px solid #e5e7eb',
        borderRadius: '8px',
        minWidth: 200,
        minHeight: 64,
        boxShadow: selected
          ? `0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 2px ${nodeColor}40`
          : '0 2px 4px rgba(0, 0, 0, 0.1)',
        transition: 'all 0.2s ease',
        position: 'relative',
        cursor: 'pointer',
        '&:hover': {
          boxShadow: `0 4px 12px rgba(0, 0, 0, 0.15)`,
          borderColor: nodeColor,
        },
      }}
    >
      {/* Input Handle - Coze Studio Style */}
      {hasInputs && (
        <Handle
          type="target"
          position={Position.Left}
          style={{
            background: '#ffffff',
            border: `2px solid ${nodeColor}`,
            width: 10,
            height: 10,
            left: -5,
            borderRadius: '50%',
            boxShadow: 'none',
          }}
        />
      )}

      {/* Coze Studio Style Node Content */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          padding: '12px 16px',
          gap: 2,
        }}
      >
        {/* Icon */}
        <Box
          sx={{
            width: 24,
            height: 24,
            borderRadius: '4px',
            backgroundColor: `${nodeColor}20`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '14px',
            flexShrink: 0,
          }}
        >
          {config.icon}
        </Box>

        {/* Node Info */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography
            variant="body2"
            fontWeight={500}
            fontSize="14px"
            color="#1f2937"
            sx={{
              lineHeight: 1.2,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {data.name}
          </Typography>
          {config.category && (
            <Typography
              variant="caption"
              color="#6b7280"
              fontSize="12px"
              sx={{
                lineHeight: 1.2,
                display: 'block',
                mt: 0.25,
              }}
            >
              {config.category}
            </Typography>
          )}
        </Box>

        {/* Status Indicator */}
        {data.executionStatus && (
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: getStatusColor(data.executionStatus),
              flexShrink: 0,
              animation: data.executionStatus === NodeExecutionStatus.RUNNING ? 'pulse 1.5s infinite' : 'none',
              '@keyframes pulse': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.5 },
                '100%': { opacity: 1 },
              },
            }}
          />
        )}
      </Box>

      {/* Output Handle - Coze Studio Style */}
      {hasOutputs && (
        <Handle
          type="source"
          position={Position.Right}
          style={{
            background: '#ffffff',
            border: `2px solid ${nodeColor}`,
            width: 10,
            height: 10,
            right: -5,
            borderRadius: '50%',
            boxShadow: 'none',
          }}
        />
      )}
    </Box>
  );
};

export default CustomNode;
