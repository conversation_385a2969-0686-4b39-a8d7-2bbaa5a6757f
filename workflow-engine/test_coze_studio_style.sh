#!/bin/bash

echo "🎨 Testing Coze Studio Style Interface..."
echo ""

# Check if both services are running
echo "1. Checking Services Status..."
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health)
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)

if [ "$BACKEND_STATUS" = "200" ]; then
    echo "✅ Backend: Running on http://localhost:8080"
else
    echo "❌ Backend: Not running (Status: $BACKEND_STATUS)"
    exit 1
fi

if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "✅ Frontend: Running on http://localhost:3000"
else
    echo "❌ Frontend: Not running (Status: $FRONTEND_STATUS)"
    exit 1
fi

# Create a test workflow to showcase the new interface
echo ""
echo "2. Creating Coze Studio Style Demo Workflow..."
DEMO_WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Coze Studio Style Demo",
    "description": "Showcase of the new Coze Studio style interface",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Workflow entry point",
        "config": {},
        "position": {"x": 100, "y": 200}
      },
      {
        "key": "llm_analyze",
        "name": "AI Analysis",
        "type": "llm",
        "description": "Analyze input with AI",
        "config": {
          "model": "gpt-4o-mini",
          "system_prompt": "You are an expert data analyst.",
          "user_prompt": "Analyze this data: {{input}}",
          "temperature": 0.7,
          "max_tokens": 300
        },
        "position": {"x": 350, "y": 150}
      },
      {
        "key": "transform_data",
        "name": "Transform Result",
        "type": "data_transformer",
        "description": "Transform analysis result",
        "config": {
          "transform_type": "uppercase",
          "source_field": "analysis",
          "target_field": "analysis_upper"
        },
        "position": {"x": 600, "y": 150}
      },
      {
        "key": "route_decision",
        "name": "Route Decision",
        "type": "conditional_router",
        "description": "Route based on analysis",
        "config": {
          "condition_field": "confidence",
          "operator": "greater_than",
          "condition_value": 0.8
        },
        "position": {"x": 350, "y": 300}
      },
      {
        "key": "send_email",
        "name": "Send Email",
        "type": "email_sender",
        "description": "Send result via email",
        "config": {
          "to": "<EMAIL>",
          "subject": "Analysis Complete",
          "body": "Analysis result: {{analysis_upper}}"
        },
        "position": {"x": 600, "y": 250}
      },
      {
        "key": "send_notification",
        "name": "Send Notification",
        "type": "notification",
        "description": "Send system notification",
        "config": {
          "type": "success",
          "message": "High confidence analysis: {{analysis}}",
          "channels": ["console", "slack"]
        },
        "position": {"x": 600, "y": 350}
      },
      {
        "key": "error_handler",
        "name": "Error Handler",
        "type": "error_handler",
        "description": "Handle any errors",
        "config": {
          "strategy": "retry",
          "max_retries": 3,
          "fallback_value": "Analysis failed"
        },
        "position": {"x": 350, "y": 450}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Workflow exit point",
        "config": {},
        "position": {"x": 850, "y": 300}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "llm_analyze"
      },
      {
        "from_node": "llm_analyze",
        "to_node": "transform_data"
      },
      {
        "from_node": "llm_analyze",
        "to_node": "route_decision"
      },
      {
        "from_node": "route_decision",
        "to_node": "send_email"
      },
      {
        "from_node": "route_decision",
        "to_node": "send_notification"
      },
      {
        "from_node": "send_email",
        "to_node": "exit"
      },
      {
        "from_node": "send_notification",
        "to_node": "exit"
      },
      {
        "from_node": "entry",
        "to_node": "error_handler"
      },
      {
        "from_node": "error_handler",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

if [ "$DEMO_WORKFLOW_ID" != "null" ] && [ "$DEMO_WORKFLOW_ID" != "" ]; then
    echo "✅ Demo workflow created: $DEMO_WORKFLOW_ID"
else
    echo "❌ Failed to create demo workflow"
fi

echo ""
echo "🎉 Coze Studio Style Interface Ready!"
echo ""
echo "🎨 New Features Implemented:"
echo "   ✅ Coze Studio Style Nodes:"
echo "      • Clean white background with subtle borders"
echo "      • Compact horizontal layout"
echo "      • Color-coded icons in rounded containers"
echo "      • Professional typography"
echo "      • Precise connection points"
echo ""
echo "   ✅ Modern Node Palette:"
echo "      • Clean category headers"
echo "      • Card-style node items"
echo "      • Hover effects with color highlights"
echo "      • Compact and organized layout"
echo ""
echo "   ✅ Professional Toolbar:"
echo "      • Clean white background"
echo "      • Blue primary action button"
echo "      • Proper spacing and typography"
echo "      • Status indicators"
echo ""
echo "   ✅ Coze Studio Color Scheme:"
echo "      • Entry/Start: Green (#22c55e)"
echo "      • Exit/End: Red (#ef4444)"
echo "      • LLM/AI: Blue (#3b82f6)"
echo "      • Data Transform: Purple (#8b5cf6)"
echo "      • Notifications: Orange (#f59e0b)"
echo "      • Email: Cyan (#06b6d4)"
echo "      • Routing: Pink (#ec4899)"
echo "      • Error Handling: Orange (#f97316)"
echo ""
echo "🌐 Access the New Interface:"
echo "   • Main App: http://localhost:3000"
echo "   • Demo Workflow: http://localhost:3000/workflows/$DEMO_WORKFLOW_ID/edit"
echo "   • New Workflow: http://localhost:3000/workflows/new/edit"
echo ""
echo "🎯 Try These Features:"
echo "   1. Drag components from the left panel"
echo "   2. Connect nodes by dragging between connection points"
echo "   3. Click nodes to see the clean selection style"
echo "   4. Use the toolbar controls for zoom and execution"
echo "   5. Notice the professional color coding"
echo ""
echo "💡 The interface now looks and feels just like Coze Studio!"
